import pandas as pd
import requests
import time
from tkinter import Tk, filedialog, Label, Frame, Canvas, PhotoImage, ttk, Toplevel, Checkbutton, Button, Scrollbar, Listbox, StringVar, BooleanVar, LabelFrame, VERTICAL, RIGHT, Y, BOTH, <PERSON><PERSON><PERSON><PERSON><PERSON>, END
import base64
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed
import threading
from tqdm import tqdm
import re
import os

# List of common VirusTotal vendors (can be expanded)
COMMON_VT_VENDORS = sorted([
    "Microsoft", "Google", "Kaspersky", "McAfee", "Symantec", "CrowdStrike", 
    "Palo Alto Networks", "ESET", "TrendMicro", "BitDefender", "Sophos", 
    "Avast", "AVG", "Malwarebytes", "F-Secure", "Fortinet", "Check Point", 
    "Cisco", "SentinelOne", "Cybereason", "GData", "Comodo", "VIPRE", 
    "Emsisoft", "ZoneAlarm", "DrWeb", "Ikarus", "ClamAV", "Bkav", "TotalDefense",
    "McAfee", "McAfee-GW-Edition"
])

# List of API keys
api_keys = [
    'd70a0b180f31c230fbc27e0704a3d58ebae6eb3a504de183e8d522cf59b4ec3b',
    '0139d5f98e260b56d7ff3c96e276406de36a2e2430da683d6e92865531223d66',
    '08e131fb5f477e6c4333476e2b99f9a7ba7c586a3bad4c39a43a1756fd7c27ac',
    '78cb6e7581f918142e4ac3080d6112b1833c3736392e5f4d63792f78888edfca',
    'c44515ee67dcb60a84d1930905a8e55dc4eb70c1e49bb165cdf938d1f3f930f7',
    'cf651d39e830f9eb24817e9b4fda19c267dfa69106b847411602fbafc40d7d5d',
    '352bcd0eefc96877c5dc26b13a7449b1b85168fa307fe4c39dd394ac75450ab4',
]

# Class to manage API keys safely across threads
class APIKeyManager:
    def __init__(self, api_keys):  # Fixed typo from _init_ to __init__
        self.api_keys = api_keys.copy()
        self.lock = threading.Lock()
        self.index = 0  # Index of the next API key to use

    def get_api_key(self):
        with self.lock:
            if not self.api_keys:
                return None
            api_key = self.api_keys[self.index % len(self.api_keys)]
            self.index += 1
            return api_key

    def remove_api_key(self, api_key):
        with self.lock:
            if api_key in self.api_keys:
                self.api_keys.remove(api_key)
                if self.index >= len(self.api_keys):
                    self.index = 0

# Function to normalize IOCs
def normalize_ioc(ioc):
    ioc = ioc.replace('[.]', '.').replace('(.)', '.')
    ioc = ioc.replace('[://]', '://').replace('[:]', ':').replace('[', '').replace(']', '')
    ioc = ioc.replace('hxxp://', 'http://').replace('hxxps://', 'https://')
    return ioc.strip()

# Function to extract IOCs from Excel and categorize them
def extract_iocs_from_excel(excel_file):
    try:
        df = pd.read_excel(excel_file)
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return {}, 0, 0  # Return empty dict and zero counts on error

    iocs = {
        'URL': [],
        'FileHash-MD5': [],
        'FileHash-SHA1': [],
        'FileHash-SHA256': [],
        'domain': [],
        'hostname': [],
        'ip': []
    }
    seen_iocs = set()
    total_read = 0
    duplicates_skipped = 0

    # Determine columns to read (Type in first, IOC in second)
    if len(df.columns) < 2:
        print("Error: Excel file must have at least two columns (Type and IOC).")
        return {}, 0, 0

    type_col_index = 0
    ioc_col_index = 1

    for index, row in df.iterrows():
        # Check if essential columns exist and are not NaN
        if pd.isna(row.iloc[type_col_index]) or pd.isna(row.iloc[ioc_col_index]):
            continue # Skip rows with missing type or IOC

        total_read += 1
        ioc_type_raw = str(row.iloc[type_col_index]).lower().strip()
        # Normalize the IOC value immediately
        ioc_value = normalize_ioc(str(row.iloc[ioc_col_index]))

        if not ioc_value: # Skip empty IOC values after normalization
            continue

        # Deduplication check
        if ioc_value in seen_iocs:
            duplicates_skipped += 1
            continue
        seen_iocs.add(ioc_value)

        # Categorize IOC based on type string
        if 'url' in ioc_type_raw:
            iocs['URL'].append(ioc_value)
        elif 'domain' in ioc_type_raw:
            iocs['domain'].append(ioc_value)
        elif 'hostname' in ioc_type_raw:
            iocs['hostname'].append(ioc_value)
        elif 'ip' in ioc_type_raw:
            # Basic IP format check (very basic)
            if re.match(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$', ioc_value):
                iocs['ip'].append(ioc_value)
            # Handle potential hostnames/domains mistakenly labeled as IP
            elif '.' in ioc_value:
                 iocs['hostname'].append(ioc_value)
            # else: skip malformed IP-like values
        elif 'md5' in ioc_type_raw:
            # Basic hash length check
             if len(ioc_value) == 32:
                iocs['FileHash-MD5'].append(ioc_value)
        elif 'sha1' in ioc_type_raw:
             if len(ioc_value) == 40:
                iocs['FileHash-SHA1'].append(ioc_value)
        elif 'sha256' in ioc_type_raw:
             if len(ioc_value) == 64:
                iocs['FileHash-SHA256'].append(ioc_value)
        # Add more specific checks or a default category if needed
        # else:
            # print(f"Warning: Unrecognized IOC type '{ioc_type_raw}' for value '{ioc_value}' - Skipping.")

    # Note: The requested prioritization (SHA256 > MD5 > SHA1) isn't directly
    # applicable here because deduplication is based on the IOC *value*.
    # If the input has SHA256, MD5, and SHA1 for the same file *as separate rows*,
    # they will be treated as unique IOCs unless their hash values are identical (impossible).
    # The current structure processes whatever hash types are provided and deduplicates
    # based purely on the hash value itself.

    return iocs, total_read, duplicates_skipped

# Function to standardize software names
def standardize_software_name(names, file_hash, vendors_results):
    if not names:
        return {"standard_name": "Unknown", "confidence": "Low", "classification": "Unknown"}
    
    # Convert names list to lowercase for better matching
    names = [name.lower() for name in names]
    
    # Common legitimate software patterns
    legitimate_patterns = {
        r'microsoft|msft': 'Microsoft',
        r'chrome|google': 'Google',
        r'firefox|mozilla': 'Mozilla',
        r'adobe': 'Adobe',
        r'oracle|java': 'Oracle',
        r'vmware': 'VMware',
        r'python': 'Python',
        r'node\.?js': 'Node.js',
        r'winzip|7zip|7-zip': 'Compression Tool',
        r'notepad\+\+': 'Notepad++',
        r'visual studio|vscode': 'Visual Studio'
    }
    
    # Check for legitimate software matches
    for pattern, vendor in legitimate_patterns.items():
        if any(re.search(pattern, name) for name in names):
            # Count malicious detections
            malicious_count = sum(1 for result in vendors_results.values() 
                                if result.get('category') == 'malicious')
            
            if malicious_count == 0:
                classification = "Legitimate"
                confidence = "High"
            elif malicious_count < 3:
                classification = "Suspicious (Low Risk)"
                confidence = "Medium"
            else:
                classification = "Suspicious (High Risk)"
                confidence = "Medium"
                
            return {
                "standard_name": f"{vendor} Software",
                "confidence": confidence,
                "classification": classification
            }
    
    # Handle potentially malicious software
    # Get the most common name
    if names:
        most_common = max(set(names), key=names.count)
        # Clean up the name
        cleaned_name = re.sub(r'[^\w\s\-\.]', '', most_common).strip()
        cleaned_name = re.sub(r'\s+', ' ', cleaned_name)
        
        malicious_count = sum(1 for result in vendors_results.values() 
                            if result.get('category') == 'malicious')
        
        if malicious_count > 5:
            classification = "Malicious"
            confidence = "High"
        elif malicious_count > 0:
            classification = "Suspicious"
            confidence = "Medium"
        else:
            classification = "Unknown"
            confidence = "Low"
            
        return {
            "standard_name": cleaned_name.title(),
            "confidence": confidence,
            "classification": classification
        }
    
    return {
        "standard_name": "Unknown",
        "confidence": "Low",
        "classification": "Unknown"
    }

# Function to check IOCs using VirusTotal API with enhanced data
def check_ioc_virustotal(ioc, ioc_type, api_key_manager, selected_vendors=None, delay=2):
    while True:
        api_key = api_key_manager.get_api_key()
        if api_key is None:
            return {
                "ioc": ioc,
                "ioc_type_input": ioc_type, # Keep track of original type
                "result": "Error: All API keys exhausted",
                "malicious_count": 0,
                "app_names": ["N/A"],
                "hashes": {}, # Store fetched hashes
                "detected_by_vendors": [], # Added
                "software_info": {
                    "standard_name": "Unknown",
                    "confidence": "Low",
                    "classification": "Error"
                }
            }

        headers = {"x-apikey": api_key}
        is_file_hash = ioc_type in ["FileHash-MD5", "FileHash-SHA1", "FileHash-SHA256"]

        # Normalize and prepare URL based on IOC type
        if ioc_type == "URL":
            ioc_clean = normalize_ioc(ioc)
            ioc_encoded = base64.urlsafe_b64encode(ioc_clean.encode()).decode().strip("=")
            url = f"https://www.virustotal.com/api/v3/urls/{ioc_encoded}"
        elif ioc_type == "ip":
            ioc_clean = normalize_ioc(ioc)
            url = f"https://www.virustotal.com/api/v3/ip_addresses/{ioc_clean}"
        elif is_file_hash:
            ioc_clean = normalize_ioc(ioc)
            url = f"https://www.virustotal.com/api/v3/files/{ioc_clean}"
        elif ioc_type in ["domain", "hostname"]:
            ioc_clean = normalize_ioc(ioc)
            url = f"https://www.virustotal.com/api/v3/domains/{ioc_clean}"
        else:
            return {
                "ioc": ioc,
                "ioc_type_input": ioc_type,
                "result": f"Unknown IOC type: {ioc_type}",
                "malicious_count": 0,
                "app_names": ["N/A"],
                "hashes": {},
                "detected_by_vendors": [], # Added
                "software_info": {
                    "standard_name": "Unknown",
                    "confidence": "Low",
                    "classification": "Error"
                }
            }

        try:
            time.sleep(delay)
            # Use tqdm.write for logging within threads
            tqdm.write(f"Checking {ioc_type}: {ioc_clean} (Using key {api_key[:8]}...)")
            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                data = response.json()
                if "data" in data and "attributes" in data["data"]:
                    attributes = data["data"]["attributes"]
                    all_vendors_results = attributes.get("last_analysis_results", {})

                    # --- Filter vendors based on selection ---
                    vendors_to_consider = {}
                    if selected_vendors is None or selected_vendors == "All":
                        vendors_to_consider = all_vendors_results
                    else:
                        # Filter by the provided list/set of vendor names (case-insensitive compare)
                        selected_vendors_lower = {v.lower() for v in selected_vendors}
                        vendors_to_consider = {
                            vendor_name: result
                            for vendor_name, result in all_vendors_results.items()
                            if vendor_name.lower() in selected_vendors_lower
                        }
                    # --- End Filter ---

                    # Count malicious vendors *from the considered list*
                    malicious_count = 0
                    detecting_vendors_list = []
                    for vendor_name, result in vendors_to_consider.items():
                        if result.get("category") == "malicious":
                            malicious_count += 1
                            detecting_vendors_list.append(vendor_name) # Store the name

                    # Get raw app names and software information (using all vendor results for context)
                    names = attributes.get("names", []) if is_file_hash else []
                    app_names = names if names else ["N/A"]
                    # Pass all vendor results to standardization for better context
                    software_info = standardize_software_name(names, ioc_clean, all_vendors_results)

                    # Extract hashes if it's a file hash type
                    file_hashes = {}
                    if is_file_hash:
                        file_hashes['sha256'] = attributes.get('sha256')
                        file_hashes['sha1'] = attributes.get('sha1')
                        file_hashes['md5'] = attributes.get('md5')
                        # Remove None values
                        file_hashes = {k: v for k, v in file_hashes.items() if v}

                    # Determine result text based on filtered count
                    if malicious_count > 0:
                        result_text = f"Malicious (detected by {malicious_count} selected vendors)"
                    else:
                        result_text = "Not malicious or undetected by selected vendors"

                    return {
                        "ioc": ioc, # Return original IOC for reference
                        "ioc_type_input": ioc_type,
                        "result": result_text,
                        "malicious_count": malicious_count, # Based on selected vendors
                        "app_names": app_names,
                        "hashes": file_hashes, # Include extracted hashes
                        "detected_by_vendors": detecting_vendors_list, # Added
                        "software_info": software_info
                    }
                else:
                    # Handle cases where VT found the IOC but has no analysis data (rare)
                    return {
                        "ioc": ioc,
                        "ioc_type_input": ioc_type,
                        "result": "No analysis data available in VirusTotal",
                        "malicious_count": 0,
                        "app_names": ["N/A"],
                        "hashes": {},
                        "detected_by_vendors": [], # Added
                        "software_info": {
                            "standard_name": "Unknown",
                            "confidence": "Low",
                            "classification": "Unknown"
                        }
                    }

            elif response.status_code == 401:
                tqdm.write(f"Unauthorized: Invalid API key {api_key[:8]}. Removing key.")
                api_key_manager.remove_api_key(api_key)
                continue # Retry with the next key
            elif response.status_code == 403 or response.status_code == 429:
                tqdm.write(f"API key quota exhausted for key {api_key[:8]}. Removing key.")
                api_key_manager.remove_api_key(api_key)
                continue # Retry with the next key
            elif response.status_code == 404:
                return {
                    "ioc": ioc,
                    "ioc_type_input": ioc_type,
                    "result": "Not found in VirusTotal",
                    "malicious_count": 0,
                    "app_names": ["N/A"],
                    "hashes": {},
                    "detected_by_vendors": [], # Added
                    "software_info": {
                        "standard_name": "Unknown",
                        "confidence": "Low",
                        "classification": "Not Found"
                    }
                 }
            else:
                # Handle other HTTP errors
                tqdm.write(f"HTTP Error {response.status_code} for IOC {ioc_clean} with key {api_key[:8]}. Retrying might be needed.")
                # Optionally retry or return error immediately
                return {
                    "ioc": ioc,
                    "ioc_type_input": ioc_type,
                    "result": f"HTTP Error {response.status_code}",
                    "malicious_count": 0,
                    "app_names": ["N/A"],
                    "hashes": {},
                    "detected_by_vendors": [], # Added
                    "software_info": {
                        "standard_name": "Unknown",
                        "confidence": "Low",
                        "classification": "Error"
                    }
                }

        except requests.exceptions.RequestException as e:
            tqdm.write(f"Network Error processing IOC {ioc_clean} with API key {api_key[:8]}: {e}. Removing key and retrying.")
            api_key_manager.remove_api_key(api_key) # Remove potentially problematic key
            continue # Retry with the next key
        except Exception as e:
            tqdm.write(f"Unexpected Error processing IOC {ioc_clean} with API key {api_key[:8]}: {e}. Removing key.")
            api_key_manager.remove_api_key(api_key) # Remove key on unexpected error
            # Return error instead of retrying indefinitely for unexpected errors
            return {
                "ioc": ioc,
                "ioc_type_input": ioc_type,
                "result": f"Unexpected Processing Error: {e}",
                "malicious_count": 0,
                "app_names": ["N/A"],
                "hashes": {},
                "detected_by_vendors": [], # Added
                "software_info": {
                    "standard_name": "Unknown",
                    "confidence": "Low",
                    "classification": "Error"
                }
            }

# Function to assign severity based on classification and malicious count
def assign_severity(result_dict):
    classification = result_dict["software_info"]["classification"]
    malicious_count = result_dict["malicious_count"]
    
    # High severity - Malicious items
    if classification == "Malicious" or "Malicious" in classification or malicious_count >= 5:
        return "High"
    # Low severity - Unverified items
    elif classification == "Unknown" or "Not Found" in classification or "Error" in classification:
        return "Low"
    # Very Low severity - Detected but not malicious or legitimate software
    elif classification == "Legitimate" or malicious_count == 0:
        return "Very Low"
    # Default to Low for anything else
    else:
        return "Low"

# Function to get filename from app names (no suffix)
def get_filename(app_names_list):
    if app_names_list and app_names_list != ["N/A"]:
        # Clean up potential issues like empty strings or None in the list
        valid_names = [name for name in app_names_list if name and isinstance(name, str)]
        if valid_names:
            # Return the first valid filename
            first_name = valid_names[0]
            # Handle cases where VirusTotal might return a list within the list
            if isinstance(first_name, list) and first_name:
                first_name = first_name[0]

            # Ensure it's a string before returning
            if isinstance(first_name, str):
                 return first_name # Return name directly without suffix

    return "Unknown" # Default if no valid name found

# Function to process IOCs concurrently
def process_iocs_concurrently(iocs, api_keys, selected_vendors=None):
    results = []
    unique_iocs_count = sum(len(ioc_list) for ioc_list in iocs.values())
    api_key_manager = APIKeyManager(api_keys)

    with ThreadPoolExecutor(max_workers=10) as executor:
        future_to_ioc = {}
        for ioc_type, ioc_list in iocs.items():
            for ioc in ioc_list:
                # Pass selected_vendors to the check function
                future = executor.submit(check_ioc_virustotal, ioc, ioc_type, api_key_manager, selected_vendors)
                future_to_ioc[future] = (ioc, ioc_type)

        with tqdm(total=unique_iocs_count, desc="Processing IOCs") as pbar:
            for future in as_completed(future_to_ioc):
                ioc_input, ioc_type_input = future_to_ioc[future]
                try:
                    result_dict = future.result()

                    # --- Apply Hash Prioritization ---
                    final_ioc = result_dict["ioc"] # Start with the original IOC input
                    final_type = result_dict["ioc_type_input"]
                    is_file_hash = final_type in ["FileHash-MD5", "FileHash-SHA1", "FileHash-SHA256"]
                    hashes = result_dict.get("hashes", {})

                    if is_file_hash and hashes:
                        if hashes.get('sha256'):
                            final_ioc = hashes['sha256']
                            final_type = "FileHash-SHA256"
                        elif hashes.get('md5'):
                            final_ioc = hashes['md5']
                            final_type = "FileHash-MD5"
                        elif hashes.get('sha1'):
                            final_ioc = hashes['sha1']
                            final_type = "FileHash-SHA1"
                        # If VT didn't return any hashes (edge case), keep original input

                    # Assign severity level
                    severity = assign_severity(result_dict)
                    # Get filename from app names list
                    filename = get_filename(result_dict.get("app_names", [])) # Pass the list
                    # Get detecting vendors
                    detecting_vendors_list = result_dict.get("detected_by_vendors", [])
                    detected_by_str = ", ".join(detecting_vendors_list) if detecting_vendors_list else "Not Detected"

                    # Append processed info
                    results.append({
                        "Type": final_type, # Use prioritized type
                        "IOC": final_ioc, # Use prioritized IOC value
                        "Result": result_dict["result"], # Keep raw result for summary stats
                        "Severity": severity,
                        "Filename": filename,
                        "Detected By": detected_by_str # Added new column data
                    })
                except Exception as exc:
                    # Log exception details for debugging
                    tqdm.write(f"IOC {ioc_input} (Type: {ioc_type_input}) generated an exception during result processing: {exc}")
                    # Add a placeholder for failed processing
                    results.append({
                        "Type": ioc_type_input,
                        "IOC": ioc_input,
                        "Result": f"Processing Error: {exc}",
                        "Severity": "Low", # Default severity for processing errors
                        "Filename": "Unknown",
                        "Detected By": "Error" # Indicate error
                    })
                finally:
                    pbar.update(1) # Ensure pbar updates even on exception

    return results

# Function to output results to a structured Excel file with summary
def output_to_excel(results, output_base, total_read, duplicates_skipped):
    print("[Debug] Entering output_to_excel...") # Debug print
    # Add extension if not provided
    if not output_base.endswith('.xlsx'):
        output_file = f"{output_base}.xlsx"
    else:
        output_file = output_base

    # ---- Sheet 1: IOC Severity Breakdown ----
    print("[Debug] Creating DataFrame from results...") # Debug print
    df_results = pd.DataFrame(results)

    # --- Final Deduplication based on prioritized IOC --- 
    if not df_results.empty:
        print("[Debug] Performing final deduplication...") # Debug print
        df_results.drop_duplicates(subset=['IOC'], keep='first', inplace=True)
        print("[Debug] Deduplication complete.") # Debug print
    else:
        print("[Debug] Results DataFrame is empty, skipping deduplication.") # Debug print


    # Define columns for the severity sheet
    severity_columns = ["Type", "IOC", "Filename", "Detected By", "Severity"]

    # Filter data by severity *after* deduplication
    print("[Debug] Filtering data by severity...") # Debug print
    high_severity_df = df_results[df_results["Severity"] == "High"][severity_columns].copy()
    low_severity_df = df_results[df_results["Severity"] == "Low"][severity_columns].copy()
    very_low_severity_df = df_results[df_results["Severity"] == "Very Low"][severity_columns].copy()
    print("[Debug] Filtering complete.") # Debug print

    # ---- Sheet 2: Processing Summary ----
    # Recalculate counts based on the deduplicated dataframe
    print("[Debug] Calculating summary statistics...") # Debug print
    total_processed_final = len(df_results)
    not_found_count = sum(1 for index, r in df_results.iterrows() if "Not found" in r.get("Result", ""))
    error_count = sum(1 for index, r in df_results.iterrows() if "Error" in r.get("Result", "") or "Unauthorized" in r.get("Result", ""))
    successful_count = total_processed_final - not_found_count - error_count

    summary_data = {
        "Metric": [
            "Total IOC Rows Read from Input",
            "Duplicate IOC Values Skipped (Initial Input)",
            "Total Unique IOCs Submitted for Analysis (Pre-Deduplication)",
            "Total IOCs After Final Deduplication (by IOC value)", # Added clarification
            "IOCs Successfully Analyzed by VirusTotal",
            "IOCs Not Found in VirusTotal",
            "Errors During VirusTotal Check/Processing",
            "High Severity IOCs Found (Final)", # Added clarification
            "Low Severity IOCs Found (Final)", # Added clarification
            "Very Low Severity IOCs Found (Final)" # Added clarification
        ],
        "Count": [
            total_read,
            duplicates_skipped, 
            len(results), # Total unique IOCs before final deduplication
            total_processed_final,
            successful_count,
            not_found_count,
            error_count,
            len(high_severity_df),
            len(low_severity_df),
            len(very_low_severity_df)
        ]
    }
    df_summary = pd.DataFrame(summary_data)
    print("[Debug] Summary statistics calculated.") # Debug print

    # ---- Write to Excel ----
    print("[Debug] Starting Excel write process...") # Debug print
    try:
        print("[Debug] Entering ExcelWriter context manager...") # Debug print
        with pd.ExcelWriter(output_file, engine="openpyxl") as writer:
            current_row = 0
            print("[Debug] Writing Severity Sheet...") # Debug print
            # --- Write Severity Sheet ---
            sheet1_name = "IOC Severity Breakdown"
            # Check if the default 'Sheet' exists and remove it
            if 'Sheet' in writer.book.sheetnames:
                 del writer.book['Sheet']

            # Create the severity sheet if it doesn't exist (it shouldn't after deleting 'Sheet')
            if sheet1_name not in writer.book.sheetnames:
                 sheet1 = writer.book.create_sheet(sheet1_name)
                 writer.sheets[sheet1_name] = sheet1 # Register the sheet
            else:
                 sheet1 = writer.sheets[sheet1_name]

            # Write High Severity
            sheet1.cell(row=current_row + 1, column=1, value="High IOCs (Malicious)")
            current_row += 1
            high_severity_df.to_excel(writer, sheet_name=sheet1_name, startrow=current_row, index=False)
            current_row += len(high_severity_df) + 2 # Add space

            # Write Low Severity
            sheet1.cell(row=current_row + 1, column=1, value="Low IOCs (Potentially Malicious/Unknown)")
            current_row += 1
            low_severity_df.to_excel(writer, sheet_name=sheet1_name, startrow=current_row, index=False)
            current_row += len(low_severity_df) + 2 # Add space

            # Write Very Low Severity
            sheet1.cell(row=current_row + 1, column=1, value="Very Low IOCs (Likely Benign/Informational)")
            current_row += 1
            very_low_severity_df.to_excel(writer, sheet_name=sheet1_name, startrow=current_row, index=False)
            print("[Debug] Severity Sheet written.") # Debug print

            # --- Write Summary Sheet ---
            print("[Debug] Writing Summary Sheet...") # Debug print
            sheet2_name = "Processing Summary"
            df_summary.to_excel(writer, sheet_name=sheet2_name, index=False)
            print("[Debug] Summary Sheet written.") # Debug print
        
        print("[Debug] Exited ExcelWriter context manager.") # Debug print
        # Print summary - Indented correctly inside the 'try' block now
        print(f"\nResults have been saved to {output_file}")
        print(f"\nResults Summary (based on final deduplicated data):")
        print(f"  High Severity IOCs: {len(high_severity_df)}")
        print(f"  Low Severity IOCs: {len(low_severity_df)}")
        print(f"  Very Low Severity IOCs: {len(very_low_severity_df)}")
        print(f"  Total IOCs in Output File: {total_processed_final}")
        print(f"  (See '{sheet2_name}' sheet in Excel for detailed stats)")

    except Exception as e:
        print(f"\n[Debug] Error during Excel write: {e}") # Debug print
        print(f"\nError writing Excel file: {e}")
        print("Attempting to save raw results (pre-deduplication) to CSV as fallback.")
        try:
            print("[Debug] Attempting CSV fallback...") # Debug print
            # Use original df_results before deduplication for CSV fallback
            csv_fallback_df = pd.DataFrame(results)
            csv_output_file = output_base.replace('.xlsx', '.csv').replace('.xls', '.csv')
            if csv_output_file == output_base: # Ensure different extension
                 csv_output_file += ".csv"
            csv_fallback_df.to_csv(csv_output_file, index=False)
            print(f"Raw results saved to {csv_output_file}")
            print("[Debug] CSV fallback successful.") # Debug print
        except Exception as csv_e:
            print(f"[Debug] CSV fallback failed: {csv_e}") # Debug print
            print(f"Could not save raw results to CSV: {csv_e}")
    print("[Debug] Exiting output_to_excel.") # Debug print


# Function to create and display vendor selection GUI
def select_vendors_gui(parent_root):
    selection = None # Variable to hold the final selection
    # Use Toplevel for a secondary window
    vendor_window = Toplevel(parent_root)
    vendor_window.title("Select VirusTotal Vendors")
    # Center the window relative to the (invisible) parent
    parent_root.update_idletasks() # Ensure parent geometry is calculated
    parent_width = 500 # Desired width
    parent_height = 450 # Desired height
    parent_x = (parent_root.winfo_screenwidth() // 2) - (parent_width // 2)
    parent_y = (parent_root.winfo_screenheight() // 2) - (parent_height // 2)
    vendor_window.geometry(f'{parent_width}x{parent_height}+{parent_x}+{parent_y}')
    vendor_window.minsize(400, 350) # Minimum size

    # --- Variables ---
    all_vendors_var = BooleanVar(value=True) # Default to selecting all
    vendor_vars = {vendor: BooleanVar(value=False) for vendor in COMMON_VT_VENDORS}

    # --- Widgets ---
    # Top Frame for 'All Vendors' Checkbox
    top_frame = LabelFrame(vendor_window, text="All Vendors", padx=10, pady=5)
    top_frame.pack(fill='x')

    # Frame for the Listbox and Scrollbar
    list_frame = LabelFrame(vendor_window, text="Specific Vendors (Select 'All' or choose from list)", padx=10, pady=10)
    list_frame.pack(padx=10, pady=(0, 10), fill='both', expand=True)

    # Bottom Frame for Buttons
    bottom_frame = LabelFrame(vendor_window, padx=10, pady=10)
    bottom_frame.pack(fill='x')

    # Listbox for vendors
    scrollbar = Scrollbar(list_frame, orient=VERTICAL)
    listbox = Listbox(list_frame, yscrollcommand=scrollbar.set, selectmode=MULTIPLE, exportselection=False, activestyle='none')
    scrollbar.config(command=listbox.yview)
    scrollbar.pack(side=RIGHT, fill=Y)
    listbox.pack(side=RIGHT, fill=BOTH, expand=True)

    for vendor in COMMON_VT_VENDORS:
        listbox.insert(END, vendor)

    # Function to enable/disable listbox based on 'All' checkbox
    def toggle_listbox_state():
        if all_vendors_var.get():
            listbox.selection_clear(0, END)
            listbox.config(state='disabled', bg='#f0f0f0') # Grey out
        else:
            listbox.config(state='normal', bg='white') # Enable

    # 'All Vendors' Checkbox
    all_checkbox = Checkbutton(top_frame, text="Use All Vendors", variable=all_vendors_var, command=toggle_listbox_state)
    all_checkbox.pack(anchor='w') # Align left

    # Initial state based on default value
    toggle_listbox_state()

    # --- Button Actions ---
    def on_confirm():
        nonlocal selection
        if all_vendors_var.get():
            selection = "All"
        else:
            selected_indices = listbox.curselection()
            selected_vendors = [listbox.get(i) for i in selected_indices]
            if not selected_vendors:
                 # If nothing selected, maybe default to 'All' or show error?
                 # For now, let's treat it as 'All' if nothing specific picked.
                 print("Warning: No specific vendors selected, defaulting to 'All'.")
                 selection = "All"
            else:
                selection = selected_vendors
        vendor_window.destroy()

    def on_cancel():
        nonlocal selection
        selection = None # Indicate cancellation
        vendor_window.destroy()

    # Buttons
    confirm_button = Button(bottom_frame, text="Confirm Selection", command=on_confirm, width=15)
    confirm_button.pack(side=RIGHT, padx=(10, 0))

    cancel_button = Button(bottom_frame, text="Cancel", command=on_cancel, width=10)
    cancel_button.pack(side=RIGHT)

    # Make the window modal - wait until it's closed
    vendor_window.grab_set()
    vendor_window.wait_window()

    return selection


# Function to create and show a modern loading splash screen
def show_splash_screen():
    splash_root = Tk()
    splash_root.title("IOC Tracker - Loading")

    # Use a cleaner theme if available (e.g., 'clam', 'alt', 'default')
    style = ttk.Style(splash_root)
    # Try themes available on most platforms
    available_themes = style.theme_names()
    if 'clam' in available_themes:
        style.theme_use('clam')
    elif 'vista' in available_themes: # Good fallback on Windows
        style.theme_use('vista')
    # Add other theme preferences if needed
    
    # Configure style for a light background
    style.configure('TFrame', background='#f0f0f0')
    style.configure('TLabel', background='#f0f0f0', foreground='#333333')
    style.configure('Title.TLabel', font=('Segoe UI', 18, 'bold'), foreground='#00529B') # Blue title
    style.configure('Status.TLabel', font=('Segoe UI', 10))
    style.configure('Footer.TLabel', font=('Segoe UI', 8), foreground='#666666')
    # Style for progress bar
    style.configure("WhiteHat.Horizontal.TProgressbar",
                    troughcolor='#e0e0e0',
                    background='#0078D4', # Microsoft Blue
                    thickness=15)

    # Get screen width and height
    screen_width = splash_root.winfo_screenwidth()
    screen_height = splash_root.winfo_screenheight()

    # Set window size and position (slightly smaller)
    width = 450
    height = 250
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    splash_root.geometry(f"{width}x{height}+{x}+{y}")

    # Remove window decorations
    splash_root.overrideredirect(True)

    # Create a main frame using ttk
    main_frame = ttk.Frame(splash_root, style='TFrame', padding=(20, 20, 20, 10))
    main_frame.pack(expand=True, fill='both')

    # Title
    title_label = ttk.Label(
        main_frame,
        text="CTI Techlab IOC Analyzer",
        style='Title.TLabel'
    )
    title_label.pack(pady=(10, 20))

    # Status Label
    status_label = ttk.Label(
        main_frame,
        text="Initializing... Please wait.",
        style='Status.TLabel'
    )
    status_label.pack(pady=5)

    # Progress Bar (Indeterminate)
    progress_bar = ttk.Progressbar(
        main_frame,
        orient='horizontal',
        mode='indeterminate',
        style="WhiteHat.Horizontal.TProgressbar",
        length=width - 60 # Adjust length based on frame padding
    )
    progress_bar.pack(pady=(10, 20))
    progress_bar.start(10) # Start the indeterminate animation

    # Footer
    footer_label = ttk.Label(
        main_frame,
        text="v1.1 - Analyzing IOCs", # Example version/status
        style='Footer.TLabel'
    )
    footer_label.pack(side="bottom", pady=(10, 0))

    # Function to close splash screen
    def close_splash():
        progress_bar.stop()
        splash_root.destroy()

    # Schedule closing the splash screen after a short delay
    # Adjust delay as needed, 2500ms = 2.5 seconds
    splash_root.after(2500, close_splash)

    splash_root.mainloop()

# Main function
def main():
    # Show the splash screen
    show_splash_screen()
    
    # Initialize Tkinter for file dialogs
    root = Tk()
    root.withdraw()

    excel_file = filedialog.askopenfilename(
        title="Select the Excel file with IOCs",
        filetypes=[("Excel Files", "*.xlsx *.xls")]
    )
    if not excel_file:
        print("No file selected, exiting.")
        return

    # Get IOCs and counts from extraction function
    iocs, total_read, duplicates_skipped = extract_iocs_from_excel(excel_file)

    # --- Add Vendor Selection --- 
    print("Displaying vendor selection...")
    selected_vendors = select_vendors_gui(root) # Call the GUI

    if selected_vendors is None:
        print("Vendor selection cancelled, exiting.")
        # Attempt to explicitly close Tkinter main loop
        try:
            root.quit()
        except Exception as tk_e:
            print(f"[Debug] Error quitting Tkinter root after cancel: {tk_e}")
        return # Exit if cancelled
    
    if selected_vendors == "All":
        print("Selected: All Vendors")
    else:
        print(f"Selected Vendors: {', '.join(selected_vendors)}")
    # --- End Vendor Selection ---

    # Check if extraction was successful
    if not iocs and total_read == 0:
         print("Exiting due to error during IOC extraction.")
         return # Exit if extraction failed critically

    print(f"Read {total_read} total IOC rows from input.")
    print(f"Skipped {duplicates_skipped} duplicate IOC values.")
    print(f"Processing {sum(len(v) for v in iocs.values())} unique IOCs...")


    results = process_iocs_concurrently(iocs, api_keys, selected_vendors)

    print("[Debug] Concurrent processing finished. Asking for save file name...") # Debug print
    output_base = filedialog.asksaveasfilename(
        title="Save Results As",
        defaultextension=".xlsx",
        filetypes=[("Excel Files", "*.xlsx")] # Only offer xlsx now
    )
    if not output_base:
        print("No output file selected, exiting.")
        return
    
    print(f"[Debug] Save file selected: {output_base}") # Debug print
    # Pass counts to the output function
    print("[Debug] Calling output_to_excel...") # Debug print
    output_to_excel(results, output_base, total_read, duplicates_skipped)
    print("[Debug] Returned from output_to_excel.") # Debug print

    # Attempt to explicitly close Tkinter main loop
    try:
        print("[Debug] Attempting to quit Tkinter root...") # Debug print
        root.quit()
        # root.destroy() # Alternatively use destroy
        print("[Debug] Tkinter root quit successful.") # Debug print
    except Exception as tk_e:
        print(f"[Debug] Error quitting Tkinter root: {tk_e}") # Debug print

    print("[Debug] End of main function.") # Debug print

if __name__ == "__main__":  
    main()